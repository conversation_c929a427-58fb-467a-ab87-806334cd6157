{% extends 'base.html' %}

{% block title %}Add Device - Hioki Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="bi bi-plus-circle me-2"></i>Add Device
        </h1>
        <p class="lead">Select device type and fill out the required details.</p>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <select id="deviceType" class="form-select">
            <option value="mqtt" selected>Hioki (MQTT Bridge)</option>
            <option value="w610">Hioki over W610</option>
            <option value="http">Hioki HTTP Device</option>
            <option value="cts">CTS Tester</option>
            <option value="ultrasonic">Ultrasonic Welder</option>
            <option value="other">Other/Custom</option>
        </select>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- MQTT Bridge Form -->
        <div id="mqttForm">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_bridge_route') }}">
                        <div class="mb-3">
                            <label for="name" class="form-label">PC Name</label>
                            <input type="text" class="form-control" id="name" name="name" required
                                placeholder="e.g., Line 1 ABAY">
                            <div class="form-text">A descriptive name for this PC</div>
                        </div>

                        <div class="mb-3">
                            <label for="ip" class="form-label">IP Address</label>
                            <input type="text" class="form-control" id="ip" name="ip" required
                                placeholder="e.g., *************"
                                pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
                            <div class="form-text">The IP address of the PC running Hioki MQTT Bridge</div>
                        </div>

                        <div class="mb-3">
                            <label for="port" class="form-label">Port</label>
                            <input type="number" class="form-control" id="port" name="port" value="5000" min="1"
                                max="65535">
                            <div class="form-text">The port number of the Hioki MQTT Bridge (default: 5000)</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Add Bridge</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- W610 Device -->
        <div id="w610Form" class="d-none">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_w610_route') }}">
                        <div class="mb-3">
                            <label for="w610_name" class="form-label">Hioki ID</label>
                            <input type="text" class="form-control" id="w610_name" name="name" required
                                placeholder="e.g., H012">
                            <div class="form-text">The ID listed on the device</div>
                        </div>




                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Add W610 Device</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- HTTP Hioki Device -->
        <div id="httpForm" class="d-none">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_http_route') }}">
                        <div class="mb-3">
                            <label for="http_name" class="form-label">Hioki ID</label>
                            <input type="text" class="form-control" id="http_name" name="name" required
                                placeholder="e.g., H012">
                        </div>

                        <div class="mb-3">
                            <label for="http_ip" class="form-label">IP Address</label>
                            <input type="text" class="form-control" id="http_ip" name="ip" required
                                placeholder="e.g., *************"
                                pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|$)){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
                        </div>

                        <div class="mb-3">
                            <label for="http_port" class="form-label">Port</label>
                            <input type="number" class="form-control" id="http_port" name="port" value="8000" min="1"
                                max="65535">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">CTS Type</label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cts_type" id="httpManifold"
                                        value="manifold" required>
                                    <label class="form-check-label" for="httpManifold">Manifold</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cts_type" id="httpEnclosure"
                                        value="enclosure" required>
                                    <label class="form-check-label" for="httpEnclosure">Enclosure</label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Add Device</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- CTS Tester -->
        <div id="ctsForm" class="d-none">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_cts_route') }}">
                        <div class="mb-3">
                            <label for="cts_name" class="form-label">CTS Name</label>
                            <input type="text" class="form-control" id="cts_name" name="name" required
                                placeholder="e.g., L1M2, L2ENC">
                            <div class="form-text">The name of the CTS Tester</div>
                        </div>

                        <div class="mb-3">
                            <label for="cts_ip" class="form-label">IP Address</label>
                            <input type="text" class="form-control" id="cts_ip" name="ip" required
                                placeholder="e.g., *************"
                                pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
                            <div class="form-text">The IP address of the CTS tester</div>
                        </div>

                        <div class="mb-3">
                            <label for="cts_port" class="form-label">Port</label>
                            <input type="number" class="form-control" id="cts_port" name="port" value="23" min="1"
                                max="65535">
                            <div class="form-text">The port number for the CTS tester (default: 23)</div>
                        </div>

                        <div class="mb-3">
                            <label for="cts_id" class="form-label">CTS ID</label>
                            <input type="text" class="form-control" id="cts_id" name="cts_id" required>
                            <div class="form-text">Identifier used in MQTT topics</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">CTS Type</label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cts_type" id="ctsManifold"
                                        value="Manifold" required>
                                    <label class="form-check-label" for="ctsManifold">Manifold</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cts_type" id="ctsEnclosure"
                                        value="Enclosure" required>
                                    <label class="form-check-label" for="ctsEnclosure">Enclosure</label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Add CTS</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Ultrasonic Welder -->
        <div id="ultrasonicForm" class="d-none">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_ultrasonic_route') }}">
                        <div class="mb-3">
                            <label for="ultra_raspi" class="form-label">Raspberry Pi Name</label>
                            <input type="text" class="form-control" id="ultra_raspi" name="raspi_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="ultra_ip" class="form-label">IP Address</label>
                            <input type="text" class="form-control" id="ultra_ip" name="ip" required
                                pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
                        </div>
                        <div class="mb-3">
                            <label for="ultra_port" class="form-label">Port</label>
                            <input type="number" class="form-control" id="ultra_port" name="port" value="8080" min="1" max="65535" disabled>
                        </div>
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Add Welder</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Other -->
        <div id="otherForm" class="d-none">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_custom_route') }}">
                        <div class="mb-3">
                            <label for="other_name" class="form-label">Device Name</label>
                            <input type="text" class="form-control" id="other_name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="other_ip" class="form-label">IP Address</label>
                            <input type="text" class="form-control" id="other_ip" name="ip" required
                                pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
                        </div>

                        <div class="mb-3">
                            <label for="other_port" class="form-label">Port</label>
                            <input type="number" class="form-control" id="other_port" name="port" value="80" min="1"
                                max="65535">
                        </div>

                        <div class="mb-3">
                            <label for="in_protocol" class="form-label">Incoming Protocol</label>
                            <select id="in_protocol" name="in_protocol" class="form-select" required>
                                <option value="webhook">Webhook/API</option>
                                <option value="hyperterminal">Hyperterminal</option>
                                <option value="mqtt">MQTT</option>
                                <option value="redis">Redis</option>
                            </select>
                        </div>

                        <div class="mb-3 d-none" id="in_mqtt_group">
                            <label for="in_mqtt_topic" class="form-label">MQTT Topic</label>
                            <input type="text" class="form-control" id="in_mqtt_topic" name="in_param">
                        </div>

                        <div class="mb-3 d-none" id="in_redis_group">
                            <label for="in_redis_channel" class="form-label">Redis Channel</label>
                            <input type="text" class="form-control" id="in_redis_channel" name="in_param">
                        </div>

                        <div class="mb-3">
                            <label for="out_protocol" class="form-label">Output Protocol</label>
                            <select id="out_protocol" name="out_protocol" class="form-select" required>
                                <option value="webhook">Webhook/API</option>
                                <option value="mqtt">MQTT</option>
                                <option value="redis">Redis</option>
                            </select>
                        </div>

                        <div class="mb-3 d-none" id="out_http_group">
                            <label for="out_http" class="form-label">HTTP Route</label>
                            <input type="text" class="form-control" id="out_http" name="out_param">
                        </div>

                        <div class="mb-3 d-none" id="out_mqtt_group">
                            <label for="out_mqtt_topic" class="form-label">MQTT Topic</label>
                            <input type="text" class="form-control" id="out_mqtt_topic" name="out_param">
                        </div>

                        <div class="mb-3 d-none" id="out_redis_group">
                            <label for="out_redis_channel" class="form-label">Redis Channel</label>
                            <input type="text" class="form-control" id="out_redis_channel" name="out_param">
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Add Device</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Information</h5>
            </div>
            <div class="card-body">
                <p>To add a device to the dashboard, you need to:</p>
                <ol>
                    <li>Select the appropriate device type</li>
                    <li>Make sure the device is accessible on the network</li>
                    <li>Enter the required information for the selected device type</li>
                </ol>
                <p>Once added, you'll be able to monitor and manage the device.</p>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Set up the device type selector
        document.getElementById('deviceType').addEventListener('change', function () {
            // Hide all forms
            ['mqttForm', 'w610Form', 'httpForm', 'ctsForm', 'ultrasonicForm', 'otherForm'].forEach(id => {
                const el = document.getElementById(id);
                if (el) el.classList.add('d-none');
            });

            const selectedForm = {
                'mqtt': 'mqttForm',
                'w610': 'w610Form',
                'http': 'httpForm',
                'cts': 'ctsForm',
                'ultrasonic': 'ultrasonicForm',
                'other': 'otherForm'
            }[this.value];

            if (selectedForm) {
                document.getElementById(selectedForm).classList.remove('d-none');
            }
        });

        // Incoming protocol handler
        const inProtocol = document.getElementById('in_protocol');
        const inMqttGroup = document.getElementById('in_mqtt_group');
        const inRedisGroup = document.getElementById('in_redis_group');

        function updateIncoming() {
            if (!inProtocol) return;
            const val = inProtocol.value;
            if (inMqttGroup) inMqttGroup.classList.toggle('d-none', val !== 'mqtt');
            if (inRedisGroup) inRedisGroup.classList.toggle('d-none', val !== 'redis');
        }

        if (inProtocol) {
            inProtocol.addEventListener('change', updateIncoming);
            updateIncoming();
        }

        // Outgoing protocol handler
        const outProtocol = document.getElementById('out_protocol');
        const outHttpGroup = document.getElementById('out_http_group');
        const outMqttGroup = document.getElementById('out_mqtt_group');
        const outRedisGroup = document.getElementById('out_redis_group');

        function updateOutgoing() {
            if (!outProtocol) return;
            const val = outProtocol.value;
            if (outHttpGroup) outHttpGroup.classList.toggle('d-none', val !== 'webhook');
            if (outMqttGroup) outMqttGroup.classList.toggle('d-none', val !== 'mqtt');
            if (outRedisGroup) outRedisGroup.classList.toggle('d-none', val !== 'redis');
        }

        if (outProtocol) {
            outProtocol.addEventListener('change', updateOutgoing);
            updateOutgoing();
        }
    });
</script>
{% endblock %}