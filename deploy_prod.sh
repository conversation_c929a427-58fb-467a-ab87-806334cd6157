#!/bin/bash
# Production deployment script for Hioki Dashboard and MQTT Bridge

# Stop on errors
set -e

echo "Starting production deployment..."

# 1. Update dependencies
echo "Updating dependencies..."
pip install -r all_requirements.txt

# 3. Run database migrations if needed
echo "Initializing database..."
python -c "from app import init_db; init_db()"

# 4. Set up systemd services (if on Linux)
if [ "$(uname)" == "Linux" ]; then
    echo "Setting up systemd services..."
    
    # Hioki Dashboard service
    cat > /tmp/hioki-dashboard.service << EOF
[Unit]
Description=Hioki Dashboard
After=network.target

[Service]
User=$(whoami)
WorkingDirectory=$(pwd)
ExecStart=$(which python) Dashboard/run_prod.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # Hioki MQTT Bridge service
    cat > /tmp/hioki-mqtt-bridge.service << EOF
[Unit]
Description=Hioki MQTT Bridge
After=network.target

[Service]
User=$(whoami)
WorkingDirectory=$(pwd)/hioki-mqtt-bridge
ExecStart=$(which gunicorn) -w 4 -b 0.0.0.0:5000 'src.app:app'
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # Install services
    sudo mv /tmp/hioki-dashboard.service /etc/systemd/system/
    sudo mv /tmp/hioki-mqtt-bridge.service /etc/systemd/system/
    
    # Reload systemd
    sudo systemctl daemon-reload
    
    # Enable and start services
    sudo systemctl enable hioki-dashboard.service
    sudo systemctl enable hioki-mqtt-bridge.service
    sudo systemctl start hioki-dashboard.service
    sudo systemctl start hioki-mqtt-bridge.service
    
    echo "Services installed and started!"
fi

echo "Deployment completed successfully!"
