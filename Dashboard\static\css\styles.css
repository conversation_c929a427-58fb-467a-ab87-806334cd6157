/* Main styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #121212;
    color: #e0e0e0;
}

/* Navbar and logo */
.proterra-logo {
    height: 30px;
    width: auto;
}

.navbar-brand {
    font-weight: 500;
}

/* Card styles */
.card {
    background-color: #1e1e1e;
    border-color: #333;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.card-header {
    background-color: #252525;
    border-bottom-color: #333;
}

.card-footer {
    background-color: #252525;
    border-top-color: #333;
}

/* Status badges */
.status-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
}

/* Bridge cards */
.bridge-card {
    transition: all 0.2s ease;
}

.bridge-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

/* Status colors */
.status-online {
    color: #28a745;
}

.status-offline {
    color: #dc3545;
}

.status-unknown {
    color: #6c757d;
}

/* Form controls */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
}

.form-control:focus, .form-select:focus {
    background-color: #333;
    border-color: #0d6efd;
    color: #fff;
}

/* Table styles */
.table {
    color: #e0e0e0;
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Device list */
.connected-devices {
    max-height: 250px;
    overflow-y: auto;
}

/* Footer */
.footer {
    margin-top: 3rem;
    border-top: 1px solid #333;
}

/* Button hover effects */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Modal styling */
.modal-content {
    background-color: #2d2d2d;
    border-color: #444;
}

.modal-header, .modal-footer {
    border-color: #444;
}

/* Alert styling */
.alert-info {
    background-color: #0d3b66;
    border-color: #0a2f52;
    color: #e0e0e0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .proterra-logo {
        height: 24px;
    }
}

/* Highlight devices in testing mode */
.table-warning {
    background-color: #fff3cd;
}

/* Add a testing indicator */
tr.table-warning td:first-child::before {
    content: "🧪 ";
}

/* Device card styles */
.device-card {
    /* ...existing styles... */
}

.device-card.testing {
    border: 2px solid #ffd700;
}

.device-status {
    padding: 4px 8px;
    border-radius: 4px;
}

.status-active {
    background-color: #4caf50;
    color: white;
}

.status-inactive {
    background-color: #f44336;
    color: white;
}

.status-unknown {
    background-color: #9e9e9e;
    color: white;
}

/* Port status styles */
.port-status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.port-status.text-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.port-status.text-danger {
    background-color: rgba(220, 53, 69, 0.1);
}
