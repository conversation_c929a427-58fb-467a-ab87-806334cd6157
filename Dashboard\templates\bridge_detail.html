{% extends 'base.html' %}

{% block title %}{{ bridge.name }} - Hioki Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="bi bi-pc-display me-2"></i>{{ bridge.name }}
        </h1>
        <p class="lead">Manage devices connected to this PC</p>
    </div>
    <div class="col-auto d-flex align-items-center">
        <span
            class="badge status-badge {% if bridge.status == 'online' %}bg-success{% else %}bg-danger{% endif %} fs-6 me-2">
            {{ bridge.status }}
        </span>
        <button id="checkStatusBtn" class="btn btn-outline-primary">
            <i class="bi bi-arrow-repeat me-1"></i>Check Status
        </button>
    </div>
</div>

{% if not bridge_reachable %}
<div class="alert alert-warning">
    <i class="bi bi-exclamation-triangle-fill me-2"></i>
    <strong>Warning:</strong> Cannot connect to the Hioki MQTT Bridge at {{ bridge.ip }}:{{ bridge.port }}.
    Please make sure the bridge is running and accessible on the network.
    <div class="mt-2">
        <p>Possible reasons for connection failure:</p>
        <ul>
            <li>The Hioki MQTT Bridge application is not running on the PC</li>
            <li>The PC is not accessible on the network</li>
            <li>A firewall is blocking the connection</li>
            <li>The IP address or port number is incorrect</li>
        </ul>
    </div>
</div>
{% endif %}

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">PC Information</h5>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>IP Address:</strong> {{ bridge.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ bridge.port }}</p>
                <p class="mb-0">
                    <strong>Last Seen:</strong>
                    {% if bridge.last_seen %}
                    <span class="last-seen-time">{{ bridge.last_seen }}</span>
                    {% else %}
                    Never
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Add New Device</h5>
            </div>
            <div class="card-body">
                <form id="addDeviceForm">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="mb-3">
                                <label for="portSelect" class="form-label">COM Port</label>
                                <select id="portSelect" name="port" class="form-select" required>
                                    <option value="">Select COM Port</option>
                                    {% for port in com_ports %}
                                    <option value="{{ port }}">{{ port }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="mb-3">
                                <label for="hiokiId" class="form-label">Hioki ID</label>
                                <input type="text" class="form-control" id="hiokiId" name="hiokiId"
                                    placeholder="e.g., H001" pattern="H\d{3}" required>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="mb-3 w-100">
                                <button type="submit" class="btn btn-primary w-100">Add</button>
                            </div>
                        </div>
                    </div>
                </form>
                <div id="addDeviceStatus" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ bridge.name }}</h5>
                <div>
                    <button id="checkStatusBtn" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-arrow-repeat me-1"></i>Check Status
                    </button>
                    <button id="removeBridgeBtn" class="btn btn-sm btn-outline-danger">
                        <i class="bi bi-trash me-1"></i>Remove PC
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if devices %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Hioki ID</th>
                                <th>COM Port</th>
                                <th>Status</th>
                                <th>Last Reading</th>
                                <th>Last Update</th>
                                <th>Port Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="devicesTableBody">
                            {% for device in devices %}
                            <tr data-device-id="{{ device.id }}" data-port="{{ device.port }}"
                                data-hioki-id="{{ device.hioki_id }}">
                                <td>{{ device.hioki_id }}</td>
                                <td>{{ device.port }}</td>
                                <td>
                                    <span
                                        class="badge status-{{ device.status }} {% if device.status == 'active' %}bg-success{% else %}bg-secondary{% endif %}">
                                        {{ device.status }}
                                    </span>
                                </td>
                                <td class="device-reading" data-hioki-id="{{ device.hioki_id }}">
                                    {{ device.last_reading if device.last_reading else 'N/A' }}
                                </td>
                                <td>
                                    {% if device.last_update %}
                                    <span class="device-timestamp last-update-time"
                                        data-hioki-id="{{ device.hioki_id }}">
                                        {{ device.last_update }}
                                    </span>
                                    {% else %}
                                    N/A
                                    {% endif %}
                                </td>
                                <td>
                                    <span
                                        class="port-status badge {% if device.port in com_ports %}text-success{% else %}text-danger{% endif %}">
                                        {% if device.port in com_ports %}Connected{% else %}Disconnected{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary test-reading-btn"
                                        data-device-id="{{ device.id }}" data-port="{{ device.port }}"
                                        data-testing="{{ 'true' if device.testing == 1 else 'false' }}">
                                        <i class="bi bi-lightning"></i> {{ 'Stop Test' if device.testing == 1 else 'Test
                                        Reading' }}
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger remove-device-btn"
                                        data-device-id="{{ device.id }}" data-port="{{ device.port }}">
                                        <i class="bi bi-trash"></i> Remove
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>No devices connected to this PC yet.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Remove Device Modal -->
<div class="modal fade" id="removeDeviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Device</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove this device?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRemoveDevice">Remove</button>
            </div>
        </div>
    </div>
</div>

<!-- Remove Bridge Modal -->
<div class="modal fade" id="removeBridgeModal" tabindex="-1" aria-labelledby="removeBridgeModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeBridgeModalLabel">Confirm PC Removal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove this PC from the dashboard?</p>
                <p class="text-danger">This will remove all associated devices and cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRemoveBridge">Remove PC</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Request current devices from the bridge
        fetch(`/api/bridges/{{ bridge.id }}/get_devices`)
            .then(response => response.json())
            .then(data => {
                console.log('Fetched devices from bridge:', data);
                // The page will be updated via the API endpoint
            })
            .catch(error => {
                console.error('Error fetching devices from bridge:', error);
            });

        // Check status button
        const checkStatusBtn = document.getElementById('checkStatusBtn');
        if (checkStatusBtn) {
            checkStatusBtn.addEventListener('click', function () {
                checkBridgeStatus({{ bridge.id }});
        });
        }

    // Add device form
    const addDeviceForm = document.getElementById('addDeviceForm');
    if (addDeviceForm) {
        addDeviceForm.addEventListener('submit', function (e) {
            e.preventDefault();
            addDevice();
        });
    }

    // Remove device buttons
    document.querySelectorAll('.remove-device-btn').forEach(button => {
        button.addEventListener('click', function () {
            const deviceId = this.getAttribute('data-device-id');
            const port = this.getAttribute('data-port');
            showRemoveDeviceModal(deviceId, port);
        });
    });

    // Confirm remove device button
    const confirmRemoveDevice = document.getElementById('confirmRemoveDevice');
    if (confirmRemoveDevice) {
        confirmRemoveDevice.addEventListener('click', function () {
            removeDevice();
        });
    }

    // Test reading buttons
    document.querySelectorAll('.test-reading-btn').forEach(button => {
        button.addEventListener('click', function () {
            const deviceId = this.getAttribute('data-device-id');
            const port = this.getAttribute('data-port');
            const isTesting = this.getAttribute('data-testing') === 'true';

            if (isTesting) {
                stopTestReading(deviceId, port);
            } else {
                startTestReading(deviceId, port);
            }
        });
    });

    // Remove bridge button
    const removeBridgeBtn = document.getElementById('removeBridgeBtn');
    if (removeBridgeBtn) {
        removeBridgeBtn.addEventListener('click', function () {
            const modal = new bootstrap.Modal(document.getElementById('removeBridgeModal'));
            modal.show();
        });
    }

    // Confirm remove bridge button
    const confirmRemoveBridge = document.getElementById('confirmRemoveBridge');
    if (confirmRemoveBridge) {
        confirmRemoveBridge.addEventListener('click', function () {
            removeBridge();
        });
    }

    // Refresh devices button
    const refreshDevicesBtn = document.getElementById('refreshDevicesBtn');
    if (refreshDevicesBtn) {
        refreshDevicesBtn.addEventListener('click', function () {
            refreshDevices();
        });
    }
    });

    // Socket.io event listeners
    socket.on('device_reading_update', function (data) {
        console.log('Received device reading update for bridge detail:', data);

        // Find all elements with the matching hioki_id
        const readingElements = document.querySelectorAll(`.device-reading[data-hioki-id="${data.hioki_id}"]`);
        const timestampElements = document.querySelectorAll(`.device-timestamp[data-hioki-id="${data.hioki_id}"]`);
        const rows = document.querySelectorAll(`tr[data-hioki-id="${data.hioki_id}"]`);

        // Update reading elements
        readingElements.forEach(element => {
            element.textContent = data.reading || 'N/A';
        });

        // Update timestamp elements
        timestampElements.forEach(element => {
            element.textContent = new Date().toLocaleString();
        });

        // Update Hioki ID if it's included in the data
        if (data.hioki_id) {
            rows.forEach(row => {
                const hiokiIdCell = row.querySelector('td:first-child');
                if (hiokiIdCell) {
                    hiokiIdCell.textContent = data.hioki_id;
                }
            });
        }

        // Update status badges in the rows
        rows.forEach(row => {
            const statusBadge = row.querySelector('.badge');
            if (statusBadge) {
                statusBadge.textContent = 'active';
                statusBadge.classList.remove('bg-secondary');
                statusBadge.classList.add('bg-success');
                statusBadge.className = statusBadge.className.replace(/status-\w+/, 'status-active');
            }
        });
    });

    // Variables to store current device being removed
    let currentDeviceId = null;
    let currentDevicePort = null;

    // Function to check bridge status
    function checkBridgeStatus(bridgeId) {
        const statusBadge = document.querySelector('.status-badge');

        // Show checking status
        if (statusBadge) {
            statusBadge.textContent = "checking...";
            statusBadge.classList.remove('bg-success', 'bg-danger');
            statusBadge.classList.add('bg-secondary');
        }

        // Make API request
        fetch(`/api/bridges/${bridgeId}/check_status`)
            .then(response => response.json())
            .then(data => {
                if (statusBadge) {
                    statusBadge.textContent = data.status;

                    if (data.status === 'online') {
                        statusBadge.classList.remove('bg-danger', 'bg-secondary');
                        statusBadge.classList.add('bg-success');
                    } else {
                        statusBadge.classList.remove('bg-success', 'bg-secondary');
                        statusBadge.classList.add('bg-danger');
                    }
                }
            })
            .catch(error => {
                console.error('Error checking status:', error);
                if (statusBadge) {
                    statusBadge.textContent = "error";
                    statusBadge.classList.remove('bg-success', 'bg-secondary');
                    statusBadge.classList.add('bg-danger');
                }
            });
    }

    // Function to add a device
    function addDevice() {
        const port = document.getElementById('portSelect').value;
        const hiokiId = document.getElementById('hiokiId').value;
        const statusDiv = document.getElementById('addDeviceStatus');

        if (!port || !hiokiId) {
            statusDiv.innerHTML = '<div class="alert alert-danger">Please select a port and enter a Hioki ID</div>';
            return;
        }

        statusDiv.innerHTML = '<div class="alert alert-info">Adding device...</div>';

        fetch(`/api/bridges/{{ bridge.id }}/add_device`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ port: port, hioki_id: hiokiId })
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    statusDiv.innerHTML = '<div class="alert alert-success">Device added successfully!</div>';
                    // Reload the page after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    statusDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.message || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                console.error('Error adding device:', error);
                statusDiv.innerHTML = '<div class="alert alert-danger">Error adding device. Please try again.</div>';
            });
    }

    // Function to refresh devices
    function refreshDevices() {
        fetch(`/api/bridges/{{ bridge.id }}/get_devices`)
            .then(response => response.json())
            .then(data => {
                // Reload the page to show updated devices
                window.location.reload();
            })
            .catch(error => {
                console.error('Error refreshing devices:', error);
                alert('Error refreshing devices. Please try again.');
            });
    }

    // Function to show remove device modal
    function showRemoveDeviceModal(deviceId, port) {
        currentDeviceId = deviceId;
        currentDevicePort = port;

        const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
        modal.show();
    }

    // Function to remove a device
    function removeDevice() {
        if (!currentDeviceId || !currentDevicePort) {
            return;
        }

        fetch(`/api/bridges/{{ bridge.id }}/devices/${currentDeviceId}/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ port: currentDevicePort })
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Close the modal
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();

                    // Reload the page to reflect changes
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error removing device:', error);
                alert('Error removing device. Please try again.');
            });
    }

    // Function to start test reading
    function startTestReading(deviceId, port) {
        fetch(`/api/bridges/{{ bridge.id }}/devices/${deviceId}/test_reading`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ port: port })
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update button state
                    const button = document.querySelector(`.test-reading-btn[data-device-id="${deviceId}"]`);
                    button.innerHTML = '<i class="bi bi-lightning"></i> Stop Test';
                    button.setAttribute('data-testing', 'true');

                    // Show success message
                    showAlert('success', 'Test mode started. Readings will not be published to MQTT.');
                } else {
                    showAlert('danger', 'Error starting test mode: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error starting test reading:', error);
                showAlert('danger', 'Error starting test mode. Please try again.');
            });
    }

    // Function to stop test reading
    function stopTestReading(deviceId, port) {
        fetch(`/api/bridges/{{ bridge.id }}/devices/${deviceId}/stop_test_reading`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ port: port })
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update button state
                    const button = document.querySelector(`.test-reading-btn[data-device-id="${deviceId}"]`);
                    button.innerHTML = '<i class="bi bi-lightning"></i> Test Reading';
                    button.setAttribute('data-testing', 'false');

                    // Show success message
                    showAlert('success', 'Test mode stopped. Readings will now be published to MQTT.');
                } else {
                    showAlert('danger', 'Error stopping test mode: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error stopping test reading:', error);
                showAlert('danger', 'Error stopping test mode. Please try again.');
            });
    }

    // Function to show alert
    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }, 5000);
    }

    // Function to remove a bridge
    function removeBridge() {
        fetch(`/api/bridges/{{ bridge.id }}/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Redirect to bridges page using the correct route name
                    window.location.href = "{{ url_for('list_bridges') }}";
                } else {
                    showAlert('danger', 'Error removing PC: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error removing PC:', error);
                showAlert('danger', 'Error removing PC. Please try again.');
            });
    }

    // Add a function to periodically refresh device data
    function refreshBridgeDevices() {
        const bridgeId = document.querySelector('[data-bridge-id]')?.getAttribute('data-bridge-id');
        if (bridgeId) {
            fetch(`/api/bridges/${bridgeId}/devices`)
                .then(response => response.json())
                .then(devices => {
                    devices.forEach(device => {
                        const rows = document.querySelectorAll(
                            `tr[data-hioki-id="${device.hioki_id}"], 
                             tr[data-device-hioki-id="${device.hioki_id}"]`
                        );

                        rows.forEach(row => {
                            // Update reading
                            const readingCell = row.querySelector('[data-reading-cell]') ||
                                row.querySelector('.device-reading') ||
                                row.querySelector('td:nth-child(4)');
                            if (readingCell && device.last_reading) {
                                readingCell.textContent = device.last_reading || 'N/A';
                            }

                            // Update timestamp
                            const timestampCell = row.querySelector('[data-timestamp-cell]') ||
                                row.querySelector('.last-update-time') ||
                                row.querySelector('td:nth-child(5)');
                            if (timestampCell && device.last_update) {
                                try {
                                    const date = new Date(device.last_update);
                                    if (!isNaN(date)) {
                                        timestampCell.textContent = date.toLocaleString();
                                    }
                                } catch (e) {
                                    console.error('Error formatting date:', e);
                                }
                            }
                        });
                    });
                })
                .catch(error => {
                    console.error('Error refreshing bridge devices:', error);
                });
        }
    }

    // Set up periodic refresh for bridge devices
    setInterval(refreshBridgeDevices, 3000); // Refresh every 3 seconds
</script>
{% endblock %}