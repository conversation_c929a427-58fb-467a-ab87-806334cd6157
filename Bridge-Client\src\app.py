from flask import Flask, session, request, redirect, url_for
from flask_socketio import SocketIO
import logging
import os
from dotenv import load_dotenv

# Import services using absolute imports
from src.services.com_service import COMService
from src.services.db_service import DBService
from src.utils.device_manager import DeviceManager

# Try to import CORS
try:
    from flask_cors import CORS

    cors_available = True
except ImportError:
    cors_available = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize socketio outside create_app to make it importable by other modules
socketio = SocketIO(
    async_mode="threading",
    cors_allowed_origins="*",
    logger=False,
    engineio_logger=False,
)

# Create device manager instance
device_manager = DeviceManager()

# Load environment variables from the project root
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
load_dotenv(os.path.join(ROOT_DIR, ".env"))


def create_app(test_config=None):
    # Create and configure the app
    app = Flask(__name__)

    # Enable CORS if available
    if cors_available:
        CORS(app)
    else:
        logger.warning(
            "Warning: flask-cors not installed. Cross-origin requests may be blocked."
        )

    # Attach SocketIO to the app
    socketio.init_app(app)

    # Load default configuration
    app.config.from_mapping(
        SECRET_KEY=os.environ.get("BRIDGE_SECRET_KEY", "bridge-secret-key"),
        LDAP_SERVER=os.environ.get("LDAP_SERVER"),
        LDAP_DOMAIN=os.environ.get("LDAP_DOMAIN", ""),
        MQTT_BROKER="vf-gateway-01",
        MQTT_PORT=1883,
        MQTT_USER="visualfactory",
        MQTT_PASSWORD="Pr0terr@",
        MQTT_KEEPALIVE=60,
        MQTT_RECONNECT_DELAY=5,
        DB_PATH=os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "data", "bridge.db"
        ),
    )

    # Override config with test config if passed
    if test_config:
        app.config.update(test_config)

    # Ensure data directory exists
    os.makedirs(os.path.dirname(app.config["DB_PATH"]), exist_ok=True)

    # Initialize database
    DBService(app.config["DB_PATH"])

    # Initialize COM service with MQTT capabilities
    com_service = COMService(
        mqtt_broker=app.config["MQTT_BROKER"],
        mqtt_port=app.config["MQTT_PORT"],
        mqtt_user=app.config["MQTT_USER"],
        mqtt_password=app.config["MQTT_PASSWORD"],
        db_path=app.config["DB_PATH"],
    )
    # Expose the COM service so routes can access MQTT status
    app.config["COM_SERVICE"] = com_service

    # Initialize device manager with services
    device_manager.initialize(
        com_service=com_service,
        socketio=socketio,
        db_path=app.config["DB_PATH"],
    )

    # Load devices from database
    db_service = DBService(app.config["DB_PATH"])
    devices = db_service.load_devices()
    device_manager.restore_devices(devices)

    # Shutdown handler
    def shutdown_handler():
        """Clean shutdown of all services"""
        logger.info("Application shutting down...")
        try:
            # Disable signal handlers during shutdown
            for sig in [signal.SIGTERM, signal.SIGINT]:
                signal.signal(sig, signal.SIG_IGN)

            # Shutdown device manager first
            device_manager.shutdown_services()

            # Allow time for cleanup
            import time

            time.sleep(0.5)

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        finally:
            logger.info("Shutdown complete")
            # Force exit after cleanup
            os._exit(0)

    # Register shutdown only once
    import atexit
    import signal

    atexit.register(shutdown_handler)

    # Register signal handlers
    for sig in [signal.SIGTERM, signal.SIGINT]:
        signal.signal(sig, lambda signum, frame: shutdown_handler())

    # Register blueprints
    from src.routes import main_bp, api_bp, auth_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp, url_prefix="/api")
    app.register_blueprint(auth_bp)

    @app.before_request
    def require_login():
        if request.path.startswith("/static/") or request.path.startswith("/socket.io/"):
            return
        if request.path.startswith("/api/"):
            return
        if request.endpoint in ("auth.login", "auth.logout"):
            return
        if not session.get("username"):
            return redirect(url_for("auth.login", next=request.url))

    # Register context processor
    from src.utils.helpers import get_local_ip, get_local_ip_addresses
    from src.version import get_full_version
    from datetime import datetime

    @app.context_processor
    def inject_globals():
        return {
            "current_year": datetime.now().year,
            "version": get_full_version(),
            "server_ip": get_local_ip(),
            "local_ips": get_local_ip_addresses(),
        }

    return app
