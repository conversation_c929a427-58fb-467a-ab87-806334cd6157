from typing import Optional, Union
import logging

logger = logging.getLogger(__name__)


def format_data(payload: str) -> Optional[float]:
    """Format Hioki reading to required float value."""
    try:
        payload = payload.strip()
        payload = float(payload)
        payload = round(payload * 1000, 4)
        return payload
    except ValueError as e:
        logger.error(f"Error formatting data: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in data formatting: {str(e)}")
        return None


def create_mqtt_topic(hioki_number: str) -> str:
    return f"nomuda/gvl/tools/GVB-Hioki-RM3545/{hioki_number}/result/impedance"


def validate_hioki_number(hioki_number: str) -> bool:
    if not hioki_number:
        return False
    return len(hioki_number) == 4 and hioki_number[0] == "H" and hioki_number[1:].isdigit()


def is_valid_reading(reading) -> bool:
    try:
        value = float(reading)
        return -9999.0 < value < 9999.0
    except Exception:
        return False


def format_reading_for_display(value: Union[float, str]) -> str:
    try:
        if isinstance(value, str):
            value = float(value)
        return f"{value:.4f} mΩ"
    except (ValueError, TypeError):
        return "N/A"
