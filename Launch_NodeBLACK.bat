@echo off
setlocal EnableExtensions EnableDelayedExpansion

set "VERSION=0.2.6-THWI"

:main
cls

REM Display ASCII art title
echo +--------------------------------------------------------+
echo ^|  _   _           _      ____  _        _    ____ _  __ ^|
echo ^| ^| \ ^| ^| ___   __^| ^|___ ^| __ )^| ^|  /\  ^| ^|  / ___^| ^|/ / ^|
echo ^| ^|  \^| ^|/ _ \ / _` ^|/ _ \^|  _ \^| ^| /  \ ^| ^| ^| ^|   ^| ' /  ^|
echo ^| ^| ^\  ^| (_) ^| (_^| ^|  __/^| ^|_) ^| ^|/ /\ \^| ^|_^| ^|___^| . \  ^|
echo ^|_^| \_^|\___/ \__,_^|\___^|^|____/^|_/_/  \_^|\___^|\____^|_^|\_\ ^|
echo +--------------------------------------------------------+

echo                      Version: %VERSION%

echo.
call :check_python

echo ----------------------------------------------------------

echo 1. Install and setup Python

echo 2. Install dependencies (all_requirements.txt)

echo 3. Start Bridge-Client

echo 4. Start Dashboard

echo 5. Exit

echo.
set /p choice="Select an option: "
if "%choice%"=="1" goto install_python
if "%choice%"=="2" goto install_requirements
if "%choice%"=="3" goto run_bridge
if "%choice%"=="4" goto run_dashboard
if "%choice%"=="5" goto end

echo Invalid choice. Please select 1-5.
pause
goto main

:check_python
where python >nul 2>&1
if %errorlevel%==0 (
    set "PY_STATUS=Active"
) else (
    set "PY_STATUS=Not installed"
)

echo Python: %PY_STATUS%
exit /b

:install_python
cls
where python >nul 2>&1 && echo Python already installed.& pause& goto main

where winget >nul 2>&1
if %errorlevel%==0 (
    echo Installing Python using winget...
    winget install -e --id Python.Python.3.11
    if %errorlevel%==0 (
        echo Python installed successfully.
    ) else (
        echo Failed to install Python via winget.
        echo Please install Python manually from https://www.python.org/downloads/
    )
) else (
    echo Winget not found. Please install Python manually from https://www.python.org/downloads/
)

pause
goto main

:install_requirements
cls
if "%PY_STATUS%"=="Not installed" (
    echo Python is not installed. Please install Python first.
    pause
    goto main
)

echo Installing Python dependencies...
python -m pip install --upgrade pip
pip install -r all_requirements.txt
if %errorlevel%==0 (
    echo Dependencies installed successfully.
) else (
    echo Failed to install dependencies. Ensure pip is configured correctly.
)
pause
goto main

:run_bridge
cls
if "%PY_STATUS%"=="Not installed" (
    echo Python is not installed. Please install Python first.
    pause
    goto main
)

echo Starting Bridge-Client...
cd /d "%~dp0Bridge-Client"
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment. Ensure that the venv module is installed.
        pause
        cd /d "%~dp0"
        goto main
    )
)
call venv\Scripts\activate
pip install -r ..\all_requirements.txt
python run.py
call venv\Scripts\deactivate
cd /d "%~dp0"
echo Bridge-Client has stopped.
pause
goto main

:run_dashboard
cls
if "%PY_STATUS%"=="Not installed" (
    echo Python is not installed. Please install Python first.
    pause
    goto main
)

echo Starting Dashboard...
cd /d "%~dp0Dashboard"
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment. Ensure that the venv module is installed.
        pause
        cd /d "%~dp0"
        goto main
    )
)
call venv\Scripts\activate
pip install -r ..\all_requirements.txt
python run_prod.py
call venv\Scripts\deactivate
cd /d "%~dp0"
echo Dashboard has stopped.
pause
goto main

:end
echo Goodbye!
pause
endlocal