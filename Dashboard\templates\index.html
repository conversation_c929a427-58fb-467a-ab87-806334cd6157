{% extends 'base.html' %}

{% block title %}NodeBLACK Dashboard - Home{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="bi bi-speedometer2 me-2"></i>NodeBLACK Dashboard
        </h1>
        <p class="lead">Central management for Wireless Device instances</p>
    </div>
    <div class="col-auto d-flex align-items-center">
        <a href="{{ url_for('add_bridge_route') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i>Add Device
        </a>
    </div>
</div>

{%- set has_any_devices =
    (bridges|length > 0) or
    (serial_devices|length > 0) or
    (http_devices|length > 0) or
    (cts_devices|length > 0) or
    (custom_devices|length > 0) or
    (w610_devices|length > 0) -%}
<div class="row">
    {% if bridges %}
    {% for bridge in bridges %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100 bridge-card" data-bridge-id="{{ bridge.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-pc-display me-2"></i>{{ bridge.name }}
                </h5>
                <span
                    class="badge status-badge {% if bridge.status == 'online' %}bg-success{% else %}bg-danger{% endif %}">
                    {{ bridge.status }}
                </span>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <p class="mb-1"><strong>IP Address:</strong> {{ bridge.ip }}</p>
                    <p class="mb-1"><strong>Port:</strong> {{ bridge.port }}</p>
                    <p class="mb-0">
                        <strong>Last Seen:</strong>
                        {% if bridge.last_seen %}
                        <span class="last-seen-time">{{ bridge.last_seen }}</span>
                        {% else %}
                        Never
                        {% endif %}
                    </p>
                </div>

                <div class="mt-3">
                    <h6 class="border-bottom pb-2">Connected Devices</h6>
                    {% set bridge_devices = [] %}
                    {% for device in devices %}
                    {% if device.bridge_id == bridge.id %}
                    {% set _ = bridge_devices.append(device) %}
                    {% endif %}
                    {% endfor %}

                    {% if bridge_devices %}
                    <div class="list-group">
                        {% for device in bridge_devices %}
                        <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                            data-device-id="{{ device.id }}" data-hioki-id="{{ device.hioki_id }}">
                            <div>
                                <strong>{{ device.hioki_id }}</strong>
                                <div class="small text-muted">Port: {{ device.port }}</div>
                                <div class="small" data-reading-cell>{{ device.last_reading or 'N/A' }}</div>
                            </div>
                            <button class="btn btn-sm btn-outline-danger remove-device-btn"
                                data-bridge-id="{{ bridge.id }}" data-device-id="{{ device.id }}"
                                data-port="{{ device.port }}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No devices connected</p>
                    {% endif %}
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('bridge_detail', bridge_id=bridge.id) }}" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-gear me-1"></i>Manage
                </a>
                <button class="btn btn-sm btn-outline-primary check-status-btn ms-1" data-bridge-id="{{ bridge.id }}">
                    <i class="bi bi-arrow-repeat me-1"></i>Check Status
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}
    {% if not has_any_devices %}
    <div class="col-12">
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>No devices have been added yet.
            <a href="{{ url_for('add_bridge_route') }}" class="alert-link">Add your first device</a>
        </div>
    </div>
    {% endif %}
</div>

{% if serial_devices %}
<h3 class="mt-4 mb-3">Serial Devices</h3>
<div class="row">
    {% for device in serial_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-serial-id="{{ device.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-usb me-2"></i>{{ device.hioki_id }}</h5>
                <span class="badge status-badge {% if device.status != 'offline' %}bg-success{% else %}bg-danger{% endif %}">{{ device.status != 'offline' and 'online' or 'offline' }}</span>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>IP Address:</strong> {{ device.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ device.port }}</p>
                <p class="mb-1"><strong>Type:</strong> {{ device.tester_type }}</p>
                <p class="mb-1"><strong>Last Reading:</strong> {{ device.last_reading or 'N/A' }}</p>
                <p class="mb-0"><strong>Last Seen:</strong> {% if device.last_update %}<span class="last-seen-time">{{ device.last_update }}</span>{% else %}Never{% endif %}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary check-serial-status-btn" data-device-id="{{ device.id }}"><i class="bi bi-arrow-repeat me-1"></i>Check Status</button>
                <button class="btn btn-sm btn-outline-danger remove-serial-btn ms-1" data-device-id="{{ device.id }}"><i class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

{% if w610_devices %}
<h3 class="mt-4 mb-3">Hioki over W610</h3>
<div class="row">
    {% for device in w610_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-w610-id="{{ device.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-wifi me-2"></i>{{ device.hioki_id }}</h5>
                <span class="badge status-badge {% if device.status != 'offline' %}bg-success{% else %}bg-danger{% endif %}">{{ 'online' if device.status != 'offline' else 'offline' }}</span>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>Type:</strong> {{ device.tester_type }}</p>
                <p class="mb-1"><strong>Last Reading:</strong> {{ device.last_reading or 'N/A' }}</p>
                <p class="mb-0"><strong>Last Seen:</strong> {% if device.last_update %}<span class="last-seen-time">{{ device.last_update }}</span>{% else %}Never{% endif %}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary check-w610-status-btn" data-device-id="{{ device.id }}"><i class="bi bi-arrow-repeat me-1"></i>Check Status</button>
                <button class="btn btn-sm btn-outline-danger remove-w610-btn ms-1" data-device-id="{{ device.id }}"><i class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

{% if http_devices %}
<h3 class="mt-4 mb-3">HTTP Hioki Devices</h3>
<div class="row">
    {% for device in http_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-http-id="{{ device.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-link-45deg me-2"></i>{{ device.hioki_id }}</h5>
                <span class="badge status-badge {% if device.status == 'online' or device.status == 'active' %}bg-success{% else %}bg-danger{% endif %}">{{ device.status }}</span>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>IP Address:</strong> {{ device.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ device.port }}</p>
                <p class="mb-1"><strong>Type:</strong> {{ device.tester_type }}</p>
                <p class="mb-1"><strong>Last Reading:</strong> {{ device.last_reading or 'N/A' }}</p>
                <p class="mb-0"><strong>Last Seen:</strong> {% if device.last_update %}<span class="last-seen-time">{{ device.last_update }}</span>{% else %}Never{% endif %}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary check-http-status-btn" data-device-id="{{ device.id }}"><i class="bi bi-arrow-repeat me-1"></i>Check Status</button>
                <button class="btn btn-sm btn-outline-danger remove-http-btn ms-1" data-device-id="{{ device.id }}"><i class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

{% if cts_devices %}
<h3 class="mt-4 mb-3">CTS Testers</h3>
<div class="row">
    {% for cts in cts_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-cts-id="{{ cts.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-box me-2"></i>{{ cts.name }}</h5>
                <span class="badge status-badge {% if cts.status == 'online' %}bg-success{% else %}bg-danger{% endif %}">{{ cts.status }}</span>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>IP Address:</strong> {{ cts.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ cts.port }}</p>
                <p class="mb-1"><strong>CTS ID:</strong> {{ cts.cts_id }}</p>
                <p class="mb-1"><strong>Type:</strong> {{ cts.cts_type }}</p>
                <p class="mb-1"><strong>Last Reading:</strong> <span class="cts-reading">{{ cts.last_reading or 'N/A' }}</span></p>
                <p class="mb-0"><strong>Last Seen:</strong> {% if cts.last_update %}<span class="last-seen-time">{{ cts.last_update }}</span>{% else %}Never{% endif %}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary check-cts-status-btn" data-cts-id="{{ cts.id }}"><i class="bi bi-arrow-repeat me-1"></i>Check Status</button>
                <button class="btn btn-sm btn-outline-danger remove-cts-btn ms-1" data-cts-id="{{ cts.id }}"><i class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

{% if welders %}
<h3 class="mt-4 mb-3">Ultrasonic Welders</h3>
<div class="row">
    {% for welder in welders %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-welder-id="{{ welder.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-hammer me-2"></i>{{ welder.welder_name or welder.raspi_name }}</h5>
                <span class="badge status-badge {% if welder.status == 'online' %}bg-success{% else %}bg-danger{% endif %}">{{ welder.status }}</span>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>Raspi:</strong> {{ welder.raspi_name }}</p>
                <p class="mb-1"><strong>IP Address:</strong> {{ welder.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ welder.port }}</p>
                <p class="mb-1"><strong>Last Data:</strong> {{ welder.last_reading or 'N/A' }}</p>
                <p class="mb-0"><strong>Last Seen:</strong> {% if welder.last_update %}<span class="last-seen-time">{{ welder.last_update }}</span>{% else %}Never{% endif %}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary check-welder-status-btn" data-welder-id="{{ welder.id }}"><i class="bi bi-arrow-repeat me-1"></i>Check Status</button>
                <button class="btn btn-sm btn-outline-secondary retry-welder-mqtt-btn ms-1" data-welder-id="{{ welder.id }}"><i class="bi bi-cloud-arrow-up me-1"></i>Retry MQTT</button>
                <button class="btn btn-sm btn-outline-danger remove-welder-btn ms-1" data-welder-id="{{ welder.id }}"><i class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

{% if custom_devices %}
<h3 class="mt-4 mb-3">Custom Devices</h3>
<div class="row">
    {% for device in custom_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-custom-id="{{ device.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-gear me-2"></i>{{ device.name }}</h5>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>IP Address:</strong> {{ device.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ device.port }}</p>
                <p class="mb-1"><strong>Incoming:</strong> {{ device.in_protocol }}</p>
                <p class="mb-0"><strong>Outgoing:</strong> {{ device.out_protocol }}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-danger remove-custom-btn" data-custom-id="{{ device.id }}"><i class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

<!-- Add Device Modal -->
<div class="modal fade" id="removeDeviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Device</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove this device?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRemoveDevice">Remove</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize variables for device removal
    let removeContext = null;

    // Setup event listeners when the DOM is loaded
    document.addEventListener('DOMContentLoaded', function () {
        // Check status buttons for bridges
        document.querySelectorAll('.check-status-btn').forEach(button => {
            button.addEventListener('click', function () {
                const bridgeId = this.getAttribute('data-bridge-id');
                checkBridgeStatus(bridgeId);
            });
        });

        // Serial device events
        document.querySelectorAll('.check-serial-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-device-id');
                checkSerialStatus(id);
            });
        });
        document.querySelectorAll('.remove-serial-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'serial', id: this.getAttribute('data-device-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // W610 device events
        document.querySelectorAll('.check-w610-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-device-id');
                checkW610Status(id);
            });
        });
        document.querySelectorAll('.remove-w610-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'w610', id: this.getAttribute('data-device-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // HTTP device events
        document.querySelectorAll('.check-http-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-device-id');
                checkHttpStatus(id);
            });
        });
        document.querySelectorAll('.remove-http-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'http', id: this.getAttribute('data-device-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // CTS device events
        document.querySelectorAll('.check-cts-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-cts-id');
                checkCtsStatus(id);
            });
        });
        document.querySelectorAll('.remove-cts-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'cts', id: this.getAttribute('data-cts-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // Ultrasonic welder events
        document.querySelectorAll('.check-welder-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-welder-id');
                checkWelderStatus(id);
            });
        });
        document.querySelectorAll('.retry-welder-mqtt-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-welder-id');
                retryWelderMqtt(id);
            });
        });
        document.querySelectorAll('.remove-welder-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'welder', id: this.getAttribute('data-welder-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // Custom device events
        document.querySelectorAll('.remove-custom-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'custom', id: this.getAttribute('data-custom-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });
    
        // Remove bridge connected device buttons
        document.querySelectorAll('.remove-device-btn').forEach(button => {
            button.addEventListener('click', function () {
                removeContext = {
                    type: 'bridge',
                    bridgeId: this.getAttribute('data-bridge-id'),
                    deviceId: this.getAttribute('data-device-id'),
                    port: this.getAttribute('data-port')
                };

                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // Confirm remove device button
        document.getElementById('confirmRemoveDevice').addEventListener('click', function () {
            if (!removeContext) return;
            if (removeContext.type === 'bridge') {
                removeDevice(removeContext.bridgeId, removeContext.deviceId, removeContext.port);
            } else if (removeContext.type === 'serial') {
                removeSerialDevice(removeContext.id);
            } else if (removeContext.type === 'w610') {
                removeW610Device(removeContext.id);
            } else if (removeContext.type === 'http') {
                removeHttpDevice(removeContext.id);
            } else if (removeContext.type === 'cts') {
                removeCtsDevice(removeContext.id);
            } else if (removeContext.type === 'welder') {
                removeWelder(removeContext.id);
            } else if (removeContext.type === 'custom') {
                removeCustomDevice(removeContext.id);
            }
        });
    });

    // Function to check bridge status
    function checkBridgeStatus(bridgeId) {
        const statusBadge = document.querySelector(`.bridge-card[data-bridge-id="${bridgeId}"] .status-badge`);

        // Show checking status
        if (statusBadge) {
            statusBadge.textContent = "checking...";
            statusBadge.classList.remove('bg-success', 'bg-danger');
            statusBadge.classList.add('bg-secondary');
        }

        // Make API request
        fetch(`/api/bridges/${bridgeId}/check_status`)
            .then(response => response.json())
            .then(data => {
                if (statusBadge) {
                    statusBadge.textContent = data.status;

                    if (data.status === 'online') {
                        statusBadge.classList.remove('bg-danger', 'bg-secondary');
                        statusBadge.classList.add('bg-success');
                    } else {
                        statusBadge.classList.remove('bg-success', 'bg-secondary');
                        statusBadge.classList.add('bg-danger');
                    }
                }
            })
            .catch(error => {
                console.error('Error checking status:', error);
                if (statusBadge) {
                    statusBadge.textContent = "error";
                    statusBadge.classList.remove('bg-success', 'bg-secondary');
                    statusBadge.classList.add('bg-danger');
                }
            });
    }

    // Function to remove a device
    function removeDevice(bridgeId, deviceId, port) {
        fetch(`/api/bridges/${bridgeId}/devices/${deviceId}/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ port: port })
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Close the modal
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();

                    // Reload the page to reflect changes
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error removing device:', error);
                alert('Error removing device. Please try again.');
            });
    }

    function removeSerialDevice(deviceId) {
        fetch(`/api/serial/${deviceId}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing serial device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeW610Device(deviceId) {
        fetch(`/api/w610/${deviceId}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing W610 device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeCtsDevice(ctsId) {
        fetch(`/api/cts/${ctsId}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing CTS device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeCustomDevice(id) {
        fetch(`/api/custom/${id}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing custom device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeHttpDevice(id) {
        fetch(`/api/http/${id}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing HTTP device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeWelder(id) {
        fetch(`/api/ultrasonic/${id}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing welder:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function checkSerialStatus(id) {
        fetch(`/api/serial/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-serial-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status === 'online');
                    badge.classList.toggle('bg-danger', data.status !== 'online');
                }
            });
    }

    function checkHttpStatus(id) {
        fetch(`/api/http/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-http-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status === 'online');
                    badge.classList.toggle('bg-danger', data.status !== 'online');
                }
            });
    }

    function checkW610Status(id) {
        fetch(`/api/w610/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-w610-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status !== 'offline');
                    badge.classList.toggle('bg-danger', data.status === 'offline');
                }
            });
    }

    function checkCtsStatus(id) {
        fetch(`/api/cts/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-cts-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status === 'online');
                    badge.classList.toggle('bg-danger', data.status !== 'online');
                }
            });
    }

    function checkWelderStatus(id) {
        fetch(`/api/ultrasonic/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-welder-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status === 'online');
                    badge.classList.toggle('bg-danger', data.status !== 'online');
                }
            });
    }

    function retryWelderMqtt(id) {
        fetch(`/api/ultrasonic/${id}/retry_mqtt`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status !== 'success') {
                    alert('Error retrying MQTT: ' + (data.message || 'unknown'));
                }
            })
            .catch(err => {
                console.error('Error retrying MQTT:', err);
            });
    }
</script>
{% endblock %}