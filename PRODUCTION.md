# Device Dashboard & MQTT Bridge Production Deployment

This document outlines the steps to deploy the Hioki Dashboard and MQTT Bridge to a production environment.

## Prerequisites

- Python 3.9+ installed
- pip package manager
- For Linux: systemd for service management
- For Windows: NSSM (Non-Sucking Service Manager) for service management

## Deployment Steps

### 1. Prepare the Environment

```bash
# Clone the repository (if not already done)
git clone https://github.com/Mentor1337/Node-BLACK
cd hioki-dashboard

# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 2. Run the Deployment Script

#### On Linux:
```bash
chmod +x deploy_prod.sh
./deploy_prod.sh
```

#### On Windows:
```
deploy_prod.bat
```

### 3. Verify the Deployment

- Dashboard should be accessible at: http://server-ip:8000
- MQTT Bridge should be running on: http://server-ip:5000

### 4. Monitoring and Maintenance

#### Service Status (Linux)
```bash
sudo systemctl status hioki-dashboard
sudo systemctl status hioki-mqtt-bridge
```

#### Service Status (Windows)
```
nssm status HiokiDashboard
nssm status HiokiMQTTBridge
```

#### Logs
- Dashboard logs: `logs/hioki_dashboard.log`
- MQTT Bridge logs: `hioki-mqtt-bridge/logs/mqtt_bridge.log`

#### Backup
Regularly backup the SQLite database:
```bash
cp hioki_dashboard.sqlite3 backups/hioki_dashboard_$(date +%Y%m%d).sqlite3
```

### 5. Troubleshooting

If you encounter issues:

1. Check the application logs
2. Verify network connectivity
3. Ensure all dependencies are installed
4. Check service status
5. Restart services if needed:
   ```bash
   # Linux
   sudo systemctl restart hioki-dashboard
   sudo systemctl restart hioki-mqtt-bridge
   
   # Windows
   nssm restart HiokiDashboard
   nssm restart HiokiMQTTBridge
   ```

## Security Considerations

1. Use HTTPS in production
2. Set up proper authentication
3. Restrict network access to the servers
4. Regularly update dependencies
5. Back up the database regularly