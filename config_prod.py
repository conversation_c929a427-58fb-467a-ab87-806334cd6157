"""
Production configuration settings for Hioki Dashboard and MQTT Bridge
"""

import os
import secrets

# Base directory of the application
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Database settings
DB_PATH = os.path.join(BASE_DIR, "hioki_dashboard.sqlite3")

# Flask settings
SECRET_KEY = secrets.token_hex(32)  # Generate a secure random key
DEBUG = False
TESTING = False

# Server settings
HOST = "0.0.0.0"
PORT = 8000  # Dashboard port
MQTT_BRIDGE_PORT = 5000

# Logging settings
LOG_LEVEL = "INFO"
LOG_FILE = os.path.join(BASE_DIR, "logs", "hioki_dashboard.log")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10 MB
LOG_BACKUP_COUNT = 5

# MQTT settings
MQTT_BROKER = "localhost"
MQTT_PORT = 1883
MQTT_KEEPALIVE = 60
MQTT_USERNAME = None  # Set to None if not using authentication
MQTT_PASSWORD = None  # Set to None if not using authentication
MQTT_CLIENT_ID = f"hioki-dashboard-{secrets.token_hex(4)}"

# Background task settings
DEVICE_CHECK_INTERVAL = 60  # seconds
DATA_RETENTION_DAYS = 30  # days to keep historical data

# Security settings
CSRF_ENABLED = True
SESSION_COOKIE_SECURE = True  # Set to False if not using HTTPS
SESSION_COOKIE_HTTPONLY = True
REMEMBER_COOKIE_HTTPONLY = True
REMEMBER_COOKIE_DURATION = 3600  # 1 hour
