{% extends 'base.html' %}

{% block title %}PCs - Hioki Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="bi bi-pc-display me-2"></i>Connected PCs
        </h1>
        <p class="lead">Manage PCs running Hioki MQTT Bridge</p>
    </div>
    <div class="col-auto d-flex align-items-center">
        <a href="{{ url_for('add_bridge_route') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i>Add Devices
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if bridges %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>IP Address</th>
                                <th>Port</th>
                                <th>Status</th>
                                <th>Last Seen</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for bridge in bridges %}
                            <tr>
                                <td>{{ bridge.name }}</td>
                                <td>{{ bridge.ip }}</td>
                                <td>{{ bridge.port }}</td>
                                <td>
                                    <span
                                        class="badge {% if bridge.status == 'online' %}bg-success{% else %}bg-danger{% endif %}">
                                        {{ bridge.status }}
                                    </span>
                                </td>
                                <td>
                                    {% if bridge.last_seen %}
                                    <span class="last-seen-time">{{ bridge.last_seen }}</span>
                                    {% else %}
                                    Never
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('bridge_detail', bridge_id=bridge.id) }}"
                                        class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-gear me-1"></i>Manage
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-emoji-tear me-2"></i>No PCs have been added yet.
                    <a href="{{ url_for('add_bridge_route') }}" class="alert-link">Add your first device(yay for
                        you!)</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}