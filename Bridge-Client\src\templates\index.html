{% extends "base.html" %}

{% block content %}
<div class="dashboard">
    <!-- Device Registration Form -->
    <section class="device-registration card">
        <h2>Add New Device</h2>
        <form id="deviceForm" class="form">
            <div class="form-group">
                <label for="portSelect">COM Port</label>
                <select id="portSelect" name="port" class="form-control" required>
                    <option value="">Select COM Port</option>
                </select>
            </div>
            <div class="form-group">
                <label for="hiokiNumber">Hioki Number</label>
                <input type="text" id="hiokiNumber" name="hiokiNumber" class="form-control" pattern="H\d{3}"
                    placeholder="e.g., H012" required>
            </div>
            <button type="submit" class="button">Add Device</button>
        </form>
    </section>

    <!-- Device List -->
    <section class="device-monitor card">
        <h2>Connected Devices</h2>
        <div id="deviceList" class="device-list">
            <!-- Devices will be populated dynamically -->
        </div>
    </section>

    <!-- Log Section -->
    <section class="system-log card">
        <div class="log-header">
            <h2>System Log</h2>
            <button id="clearLog" class="button button-secondary">Clear Log</button>
        </div>
        <div id="logContainer" class="log-container">
            <!-- Log entries will be populated dynamically -->
        </div>
    </section>
</div>
{% endblock %}


<div class="mqtt-test">
    <button id="testMqttBtn" class="button button-primary">Test MQTT Connection</button>
    <span id="mqttTestResult"></span>
</div>