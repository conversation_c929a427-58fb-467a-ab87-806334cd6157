import socket
import os
from ldap3 import Server, Connection, ALL


def get_local_ip_addresses():
    """Get all local IP addresses of the machine"""
    ip_addresses = []
    try:
        # Get all network interfaces
        hostname = socket.gethostname()
        # Get the primary IP address
        primary_ip = socket.gethostbyname(hostname)
        ip_addresses.append(primary_ip)

        # Try to get all IP addresses
        try:
            addresses = socket.getaddrinfo(hostname, None)
            for addr in addresses:
                ip = addr[4][0]
                # Filter out loopback addresses and IPv6
                if (
                    not ip.startswith("127.")
                    and ":" not in ip
                    and ip not in ip_addresses
                ):
                    ip_addresses.append(ip)
        except:
            pass
    except:
        ip_addresses.append("127.0.0.1")

    return ip_addresses


def get_local_ip():
    """Get the primary local IP address.

    This implementation avoids relying on external connectivity by
    attempting to connect to a non-routable IP (``**************``). If that
    fails, it falls back to the first non-loopback address returned by
    :func:`get_local_ip_addresses`.
    """

    try:
        # Connect to a non-routable address to determine the outgoing
        # interface. This works even when the host has no internet access.
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("**************", 1))
        ip = s.getsockname()[0]
        s.close()

        if not ip.startswith("127."):
            return ip
    except Exception:
        pass

    # Fall back to any other detected address
    try:
        for addr in get_local_ip_addresses():
            if not addr.startswith("127."):
                return addr
    except Exception:
        pass

    # As a last resort, return loopback
    return "127.0.0.1"


def authenticate_ldap(username: str, password: str) -> bool:
    """Authenticate a user against the configured LDAP server."""
    ldap_server = os.getenv("LDAP_SERVER")
    ldap_domain = os.getenv("LDAP_DOMAIN", "")
    if not ldap_server:
        return False
    user_dn = (
        f"{ldap_domain}\\{username}"
        if ldap_domain and not username.startswith(ldap_domain) and "@" not in username
        else username
    )
    try:
        server = Server(ldap_server, get_info=ALL)
        conn = Connection(server, user=user_dn, password=password, auto_bind=True)
        conn.unbind()
        return True
    except Exception:
        return False
