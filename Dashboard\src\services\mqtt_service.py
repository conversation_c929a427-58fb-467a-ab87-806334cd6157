import json
import logging
from datetime import datetime

import paho.mqtt.client as mqtt

from ..utils.data_formatter import format_data, create_mqtt_topic
from . import db_service

logger = logging.getLogger(__name__)

MQTT_BROKER = "vf-gateway-01"
MQTT_PORT = 1883
MQTT_USER = "visualfactory"
MQTT_PASSWORD = "Pr0terr@"
MQTT_TOPIC_PREFIX = "Proterra/Greenville/GVB/Pack/Hioki-Bridge/"
W610_TOPIC_PREFIX = "Proterra/NodeBlack/Hioki/v1/"

socketio = None
mqtt_client = None


def on_connect(client, userdata, flags, rc):
    logger.info(f"Connected to MQTT broker with result code {rc}")
    client.subscribe(f"{MQTT_TOPIC_PREFIX}+/status")
    client.subscribe("nomuda/gvl/tools/GVB-Hioki-RM3545/+/result/impedance")
    client.subscribe(f"{W610_TOPIC_PREFIX}#")
    client.subscribe(f"{W610_TOPIC_PREFIX}+/will")


def on_message(client, userdata, msg):
    topic = msg.topic
    payload = msg.payload.decode()
    try:
        if topic.endswith("/status"):
            bridge_id = topic.split("/")[-2]
            status = payload
            db_service.update_bridge_status(bridge_id, status)
            if socketio:
                socketio.emit("bridge_status_update", {"bridge_id": bridge_id, "status": status})
        elif "result/impedance" in topic:
            hioki_id = topic.split("/")[-3]
            data = json.loads(payload)
            device_id = db_service.update_device_reading(hioki_id, data)
            if socketio:
                socketio.emit(
                    "device_reading_update",
                    {"hioki_id": hioki_id, "reading": data["value"], "timestamp": data["timestamp"]},
                )
                if device_id:
                    socketio.emit(
                        "device_update",
                        {
                            "device_id": device_id,
                            "hioki_id": hioki_id,
                            "reading": data["value"],
                            "status": "active",
                            "timestamp": data["timestamp"],
                        },
                    )
        elif topic.startswith(W610_TOPIC_PREFIX) and topic.endswith("/will"):
            hioki_id = topic[len(W610_TOPIC_PREFIX):].split("/")[0]
            status = payload.strip().lower()
            device_id = db_service.update_device_status_by_hioki_id(hioki_id, status)
            if socketio:
                socketio.emit(
                    "device_update",
                    {
                        "device_id": device_id,
                        "hioki_id": hioki_id,
                        "status": status,
                        "timestamp": datetime.now().isoformat(),
                    },
                )
        elif topic.startswith(W610_TOPIC_PREFIX):
            hioki_id = topic[len(W610_TOPIC_PREFIX):].split("/")[0]
            raw_value = payload.split("=")[-1].strip()
            try:
                if float(raw_value) == 1e30:
                    return
            except Exception:
                pass
            formatted = format_data(raw_value)
            if formatted is not None:
                data = {"value": formatted, "timestamp": datetime.now().isoformat()}
                db_service.update_device_reading(hioki_id, data)
                if mqtt_client:
                    mqtt_client.publish(create_mqtt_topic(hioki_id), formatted, qos=1)
        elif topic.endswith("/device_added"):
            data = json.loads(payload)
            bridge = db_service.find_bridge_by_ip_port(data.get("bridge_ip"), data.get("bridge_port"))
            if bridge:
                db_service.add_device_record(bridge["id"], data.get("port"), data.get("hioki_id"))
                if socketio:
                    socketio.emit(
                        "device_added_to_dashboard",
                        {"bridge_id": bridge["id"], "hioki_id": data.get("hioki_id"), "port": data.get("port")},
                    )
        elif topic.endswith("/device_removed"):
            data = json.loads(payload)
            bridge = db_service.find_bridge_by_ip_port(data.get("bridge_ip"), data.get("bridge_port"))
            if bridge:
                db_service.remove_device_by_hioki_id(data.get("hioki_id"))
                if socketio:
                    socketio.emit(
                        "device_removed_from_dashboard",
                        {"bridge_id": bridge["id"], "hioki_id": data.get("hioki_id")},
                    )
    except Exception as e:
        logger.error(f"Error processing MQTT message: {str(e)}")


def setup_mqtt(socketio_instance=None):
    global mqtt_client, socketio
    socketio = socketio_instance
    try:
        client_id = f"hioki-dashboard-{int(datetime.now().timestamp())}"
        client = mqtt.Client(client_id=client_id)
        client.username_pw_set(MQTT_USER, MQTT_PASSWORD)
        client.on_connect = on_connect
        client.on_message = on_message
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        client.loop_start()
        mqtt_client = client
        return client
    except Exception as e:
        logger.error(f"Failed to connect to MQTT broker: {str(e)}")
        return None

