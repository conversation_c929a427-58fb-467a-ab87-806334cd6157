from typing import Optional, Union
import logging

logger = logging.getLogger(__name__)


def format_data(payload: str) -> Optional[float]:
    """
    Format the resistance meter reading according to specifications.

    Args:
        payload (str): Raw reading from Hioki resistance meter

    Returns:
        float: Formatted reading (multiplied by 1000, 4 decimal places)
        None: If formatting fails

    Example:
        >>> format_data("0.0001234")
        0.1234
    """
    try:
        # Remove whitespace
        payload = payload.strip()

        # Convert to float and handle scientific notation
        payload = float(payload)

        # Multiply by 1000 and round to 4 decimal places
        payload = round(payload * 1000, 4)

        return payload
    except ValueError as e:
        logger.error(f"Error formatting data: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in data formatting: {str(e)}")
        return None


def create_mqtt_topic(hioki_number: str) -> str:
    """
    Generate MQTT topic for a Hioki device.

    Args:
        hioki_number (str): Hioki device identifier (e.g., 'H012')

    Returns:
        str: Formatted MQTT topic

    Example:
        >>> create_mqtt_topic("H012")
        "nomuda/gvl/tools/GVB-Hioki-RM3545/H012/result/impedance"
    """
    return f"nomuda/gvl/tools/GVB-Hioki-RM3545/{hioki_number}/result/impedance"


def validate_hioki_number(hioki_number: str) -> bool:
    """
    Validate Hioki device number format.

    Args:
        hioki_number (str): Hioki device identifier to validate

    Returns:
        bool: True if valid format (H followed by 3 digits)

    Example:
        >>> validate_hioki_number("H012")
        True
        >>> validate_hioki_number("ABC")
        False
    """
    if not hioki_number:
        return False
    return (
        len(hioki_number) == 4 and hioki_number[0] == "H" and hioki_number[1:].isdigit()
    )


def is_valid_reading(reading):
    try:
        value = float(reading)
        return value < 9999.0 and value > -9999.0
    except Exception:
        return False


def format_reading_for_display(value: Union[float, str]) -> str:
    """
    Format reading for display in UI.

    Args:
        value (Union[float, str]): Reading value to format

    Returns:
        str: Formatted string with units

    Example:
        >>> format_reading_for_display(0.1234)
        "0.1234 mΩ"
    """
    try:
        if isinstance(value, str):
            value = float(value)
        return f"{value:.4f} mΩ"
    except (ValueError, TypeError):
        return "N/A"
