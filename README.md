# Hioki MQTT Bridge and Wireless Device Dashboard

## **Hioki Dashboard has been renamed to Wireless Device Dashboard**

## Overview
The Hioki MQTT Bridge is a Python application built with Flask that allows users to interact with Hioki resistance meters via a local web interface. Users can select connected COM ports, register devices, and monitor data readings, which are then published to an MQTT broker.

## Features
- User-friendly web interface for selecting COM ports and managing devices.
- Real-time data reading from Hioki resistance meters.
- Data formatting according to specified requirements.
- MQTT communication for publishing readings to a broker.
- Clean and professional UI with dark mode support.

## Project Structure
```
hioki-mqtt-bridge
├── src
│   ├── app.py                # Entry point of the Flask application
│   ├── static
│   │   ├── css
│   │   │   └── styles.css    # Styles for the application
│   │   └── js
│   │       └── main.js       # JavaScript for frontend interactions
│   ├── templates
│   │   ├── base.html         # Base HTML template
│   │   └── index.html        # Main webpage for user interactions
│   ├── services
│   │   ├── com_service.py    # Logic for COM port interactions
│   │   └── mqtt_service.py    # MQTT communication handling
│   ├── models
│   │   └── device.py         # Device model definition
│   └── utils
│       └── data_formatter.py  # Utility functions for data formatting
├── config.py                 # Configuration settings
├── all_requirements.txt      # Project dependencies
└── README.md                 # Project documentation
```

## Installation
1. Clone the repository:
   ```
   git clone <repository-url>
   cd hioki-mqtt-bridge
   ```

2. Install the required dependencies:
   ```
   pip install -r all_requirements.txt
   ```

3. Configure the application settings in `config.py`.
4. Create a `.env` file containing the LDAP variables shown below. The
   application uses **python-dotenv** to load this file automatically.

### LDAP Configuration
The dashboard and bridge authenticate users against Active Directory using LDAP.
Set the following environment variables to match your LDAP server:

```bash
LDAP_SERVER=ldap://**********
LDAP_SECONDARY_SERVER=ldap://**********
LDAP_DOMAIN=bus.local
LDAP_BASE_DN=DC=bus,DC=local
LDAP_BIND_USER=s.ignition.ldap.ro
LDAP_BIND_PASSWORD=Npf79$mFn%
```

User login sessions are stored only for the current browser instance. Closing
the browser window clears the session and requires re-authentication on the next
visit. In addition, the dashboard automatically logs users out after 12 hours.

### API Authentication
API endpoints previously required a bearer token for access. Authentication is
currently **disabled** for troubleshooting, so requests will succeed without a
token. Tokens can still be generated in case authentication is re-enabled. Send
a `POST` request to `/api/token` with JSON credentials:

```bash
curl -X POST http://<bridge-host>:5000/api/token \
  -H "Content-Type: application/json" \
  -d '{"username": "<user>", "password": "<pass>"}'
```

Include the returned token in the `Authorization` header when authentication is
enabled:

```bash
curl -H "Authorization: Bearer <token>" http://<bridge-host>:5000/api/devices
```

### Dashboard API Tokens
The dashboard exposes the same token-based mechanism. Authentication is also
disabled by default, but tokens can be obtained via
`http://<dashboard-host>:8000/api/token` for future use. When authentication is
enabled, include the token in the `Authorization` header for dashboard API
requests.

### Custom webhook response
Hioki devices that post readings to `/api/v1/hioki` may expect a specific
reply. Set the `HIOKI_HTTP_REPLY` environment variable to control this
response:

- **Unset**: return the default JSON `{"status": "success"}`.
- **Empty string**: reply with `204 No Content`.
- **Any text**: that text is returned as plain text.

## Usage
1. Run the application:
   ```
   python src/app.py
   ```

2. Open your web browser and navigate to `http://localhost:5000` to access the application.

## Troubleshooting

### xmlsec library mismatch
If you encounter an error similar to:

```
xmlsec.InternalError: (-1, 'lxml & xmlsec libxml2 library version mismatch')
```

install the system development libraries for XML security and reinstall the
`xmlsec` Python package so that it is built against the same version of `libxml2` as `lxml`:

```bash
sudo apt-get install libxml2-dev libxmlsec1-dev libxmlsec1-openssl
pip install --force-reinstall --no-binary=:all: xmlsec==1.3.13
```

Reinstalling `xmlsec` in this manner ensures compatibility with the `lxml`
package installed from `pip`.

## Contributing
Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License
This project is licensed under the MIT License. See the LICENSE file for details.
